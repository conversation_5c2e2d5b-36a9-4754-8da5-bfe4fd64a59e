<?php

namespace App\Services;

use App\Models\Coupon;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CouponService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Coupon::query();

        $query->with(['vendor:id,name,display_name_en,display_name_ar', 'user:id,name,email']);

        // Select specific columns
        $query->select(['*']);

        // Apply authorization scoping
        $this->applyAuthorizationScope($query);

        // Apply filters
        $this->applyCouponFilters($query, $request);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['code', 'title', 'title_ar', 'description', 'description_ar'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareCouponData($request);

        return Coupon::create($data);
    }

    public function show(int $id): Coupon
    {
        return Coupon::with(['vendor:id,name,display_name_en,display_name_ar', 'user:id,name,email'])
            ->findOrFail($id);
    }

    public function update(Request $request, int $id): Coupon
    {
        $coupon = Coupon::findOrFail($id);
        $data = $this->prepareCouponData($request, false);

        $coupon->update($data);

        return $coupon->fresh(['vendor:id,name,display_name_en,display_name_ar', 'user:id,name,email']);
    }

    public function destroy(int $id): bool
    {
        $coupon = Coupon::findOrFail($id);
        return $coupon->delete();
    }

    private function prepareCouponData($request, bool $isNew = true): array
    {
        $validated = $request->validated();

        // Get the fillable fields from the model
        $fillable = (new Coupon())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));

        // Ensure code is uppercase
        if (isset($data['code'])) {
            $data['code'] = strtoupper($data['code']);
        }

        // Handle vendor assignment based on user role
        if ($isNew && auth()->check()) {
            $user = auth()->user();
            $data['user_id'] = $user->id;
            $data['created_at'] = now();

            // If user is a vendor and no vendor_id is specified, assign to their vendor
            if ($user->hasRole('vendor') && !isset($data['vendor_id'])) {
                $data['vendor_id'] = $this->getUserVendorId($user);
            }

            // Only admins can create platform-wide coupons (vendor_id = null)
            if (isset($data['vendor_id']) && $data['vendor_id'] === null && !$user->hasRole('admin')) {
                $data['vendor_id'] = $this->getUserVendorId($user);
            }
        }

        return $data;
    }

    private function applyCouponFilters($query, Request $request): void
    {
        // Filter by status
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->input('type'));
        }

        // Filter by vendor
        if ($request->filled('vendor_id')) {
            $query->where('vendor_id', $request->input('vendor_id'));
        }

        // Filter platform-wide coupons
        if ($request->boolean('platform_wide')) {
            $query->whereNull('vendor_id');
        }

        // Filter by validity
        if ($request->boolean('valid_only')) {
            $query->valid();
        }

        // Filter by expiry status
        if ($request->has('expired')) {
            if ($request->boolean('expired')) {
                $query->where('end_date', '<', now());
            } else {
                $query->where(function ($q) {
                    $q->whereNull('end_date')
                      ->orWhere('end_date', '>=', now());
                });
            }
        }

        // Filter by start date range
        if ($request->filled('start_date_from')) {
            $query->where('start_date', '>=', $request->input('start_date_from'));
        }

        if ($request->filled('start_date_to')) {
            $query->where('start_date', '<=', $request->input('start_date_to'));
        }

        // Filter by end date range
        if ($request->filled('end_date_from')) {
            $query->where('end_date', '>=', $request->input('end_date_from'));
        }

        if ($request->filled('end_date_to')) {
            $query->where('end_date', '<=', $request->input('end_date_to'));
        }

        // Filter by discount value range
        if ($request->filled('min_value')) {
            $query->where('value', '>=', $request->input('min_value'));
        }

        if ($request->filled('max_value')) {
            $query->where('value', '<=', $request->input('max_value'));
        }
    }

    /**
     * Get coupons for a specific vendor
     */
    public function getVendorCoupons(int $vendorId, Request $request): Collection|LengthAwarePaginator|array
    {
        $request->merge(['vendor_id' => $vendorId]);
        return $this->index($request);
    }

    /**
     * Get platform-wide coupons
     */
    public function getPlatformCoupons(Request $request): Collection|LengthAwarePaginator|array
    {
        $request->merge(['platform_wide' => true]);
        return $this->index($request);
    }

    /**
     * Validate coupon code
     */
    public function validateCouponCode(string $code, ?int $vendorId = null): ?Coupon
    {
        $query = Coupon::where('code', strtoupper($code))->valid();

        if ($vendorId) {
            $query->where(function ($q) use ($vendorId) {
                $q->where('vendor_id', $vendorId)
                  ->orWhereNull('vendor_id'); // Include platform-wide coupons
            });
        } else {
            $query->whereNull('vendor_id'); // Only platform-wide coupons
        }

        return $query->first();
    }

    /**
     * Get coupon statistics
     */
    public function getCouponStats(?int $vendorId = null): array
    {
        $query = Coupon::query();

        // Apply authorization scoping
        $this->applyAuthorizationScope($query);

        if ($vendorId) {
            $query->where('vendor_id', $vendorId);
        }

        $total = $query->count();
        $active = $query->where('is_active', true)->count();
        $expired = $query->where('end_date', '<', now())->count();
        $upcoming = $query->where('start_date', '>', now())->count();

        return [
            'total' => $total,
            'active' => $active,
            'expired' => $expired,
            'upcoming' => $upcoming,
            'inactive' => $total - $active,
        ];
    }

    /**
     * Apply authorization scope to query based on user role
     */
    private function applyAuthorizationScope($query): void
    {
        if (!auth()->check()) {
            return;
        }

        $user = auth()->user();

        // Admin can see all coupons
        if ($user->hasRole('admin')) {
            return;
        }

        // Vendor can only see their own coupons and platform-wide coupons
        if ($user->hasRole('vendor')) {
            $vendorId = $this->getUserVendorId($user);
            $query->where(function ($q) use ($vendorId) {
                $q->where('vendor_id', $vendorId)
                  ->orWhereNull('vendor_id');
            });
        }
    }

    /**
     * Get the vendor ID associated with the user
     */
    private function getUserVendorId($user): ?int
    {
        // TODO: Implement the actual logic to get vendor ID from user
        // This is a placeholder implementation
        // You'll need to implement based on your user-vendor relationship

        // Example implementations:
        // return $user->vendor_id; // if user has direct vendor_id field
        // return $user->vendor?->id; // if user has vendor relationship
        // return $user->vendorProfile?->vendor_id; // if through a profile relationship

        return null;
    }
}
