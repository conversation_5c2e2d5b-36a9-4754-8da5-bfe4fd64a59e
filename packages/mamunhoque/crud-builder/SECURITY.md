# Security Policy

## Supported Versions

We release patches for security vulnerabilities. Which versions are eligible for receiving such patches depends on the CVSS v3.0 Rating:

| Version | Supported          |
| ------- | ------------------ |
| 1.x.x   | :white_check_mark: |

## Reporting a Vulnerability

The Laravel CRUD Builder team and community take security bugs seriously. We appreciate your efforts to responsibly disclose your findings, and will make every effort to acknowledge your contributions.

To report a security issue, please use the GitHub Security Advisory ["Report a Vulnerability"](https://github.com/mamunhoque/laravel-crud-builder/security/advisories/new) tab.

The team will send a response indicating the next steps in handling your report. After the initial reply to your report, the security team will keep you informed of the progress towards a fix and full announcement, and may ask for additional information or guidance.

### What to Include

Please include the following information in your report:

- Type of issue (e.g. buffer overflow, SQL injection, cross-site scripting, etc.)
- Full paths of source file(s) related to the manifestation of the issue
- The location of the affected source code (tag/branch/commit or direct URL)
- Any special configuration required to reproduce the issue
- Step-by-step instructions to reproduce the issue
- Proof-of-concept or exploit code (if possible)
- Impact of the issue, including how an attacker might exploit the issue

### What to Expect

- **Acknowledgment**: We will acknowledge receipt of your vulnerability report within 48 hours.
- **Assessment**: We will assess the vulnerability and determine its severity within 5 business days.
- **Fix**: We will work on a fix and coordinate the release timeline with you.
- **Disclosure**: We will publicly disclose the vulnerability after a fix is available.

### Safe Harbor

We support safe harbor for security researchers who:

- Make a good faith effort to avoid privacy violations, destruction of data, and interruption or degradation of our services
- Only interact with accounts you own or with explicit permission of the account holder
- Do not access a system or account without permission
- Report any vulnerability you've discovered promptly
- Avoid violating the privacy of others, disrupting our systems, destroying data, and/or harming user experience
- Use only the Official Channels to discuss vulnerability information with us
- Keep information about any vulnerabilities you've discovered confidential between yourself and us until we've had 90 days to resolve the issue

### Out of Scope

The following issues are considered out of scope:

- Vulnerabilities in dependencies (please report these to the respective maintainers)
- Social engineering attacks
- Physical attacks
- Attacks requiring physical access to a user's device
- Denial of service attacks
- Issues in third-party applications that integrate with our package

### Recognition

We believe in recognizing the efforts of security researchers. If you report a valid security vulnerability, we will:

- Acknowledge your contribution in our security advisories (if you wish)
- Include you in our Hall of Fame (if you wish)
- Work with you on the disclosure timeline

Thank you for helping keep Laravel CRUD Builder and our users safe!
